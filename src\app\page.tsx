'use client';

import React, { useState, useEffect } from 'react';
import { useGameStore } from '@/store/gameStore';
import { useGameLoop } from '@/hooks/useGameLoop';
import CharacterCreation from '@/components/player/CharacterCreation';
import PlayerStats from '@/components/player/PlayerStats';
import PlayerEquipment from '@/components/player/PlayerEquipment';
import NotificationSystem from '@/components/ui/NotificationSystem';

export default function Home() {
  const { player } = useGameStore();
  const [showCharacterCreation, setShowCharacterCreation] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize game loop
  useGameLoop();

  useEffect(() => {
    // Check if player exists
    if (!player || !player.name || player.name === 'Hero') {
      setShowCharacterCreation(true);
    }
    setIsLoading(false);
  }, [player]);

  const handleCharacterCreationComplete = () => {
    setShowCharacterCreation(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center game-container">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading IdleScaper...</p>
        </div>
      </div>
    );
  }

  if (showCharacterCreation) {
    return (
      <>
        <CharacterCreation onComplete={handleCharacterCreationComplete} />
        <NotificationSystem />
      </>
    );
  }

  return (
    <>
      <div className="min-h-screen game-container">
        <div className="container mx-auto p-4">
          <header className="mb-6">
            <h1 className="text-3xl font-bold text-center mb-2">
              IdleScaper
            </h1>
            <p className="text-center text-muted-foreground">
              The Ultimate Idle RPG Adventure
            </p>
          </header>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Player Stats */}
            <div className="lg:col-span-1">
              <PlayerStats />
            </div>

            {/* Middle Column - Main Game Area */}
            <div className="lg:col-span-1">
              <div className="space-y-6">
                <div className="text-center p-8 bg-muted/50 rounded-lg">
                  <h2 className="text-xl font-semibold mb-2">
                    Welcome to the City
                  </h2>
                  <p className="text-muted-foreground">
                    Your adventure begins here. Explore dungeons, fight monsters, and collect treasures!
                  </p>
                </div>

                {/* Placeholder for game areas */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-card rounded-lg border text-center">
                    <div className="text-2xl mb-2">🏰</div>
                    <h3 className="font-medium">Dungeons</h3>
                    <p className="text-sm text-muted-foreground">Coming Soon</p>
                  </div>
                  <div className="p-4 bg-card rounded-lg border text-center">
                    <div className="text-2xl mb-2">🏪</div>
                    <h3 className="font-medium">Shops</h3>
                    <p className="text-sm text-muted-foreground">Coming Soon</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Equipment */}
            <div className="lg:col-span-1">
              <PlayerEquipment />
            </div>
          </div>
        </div>
      </div>
      <NotificationSystem />
    </>
  );
}
